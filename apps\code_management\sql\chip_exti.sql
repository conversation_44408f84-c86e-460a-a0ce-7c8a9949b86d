CREATE TABLE public.chip_exti (
    id SERIAL PRIMARY KEY,
    type VARCHAR(10)[] DEFAULT '{}',
    alt_value VARCHAR(50),
    work_model VARCHAR(20)[] DEFAULT '{}',
    speed VARCHAR(10)[] DEFAULT '{}',
    up_down_pullType VARCHAR(10)[] DEFAULT '{}',
    channel VARCHAR(5)[] DEFAULT '{}',
    preemptPriority JSONB DEFAULT '{}'::jsonb,
    subPriority JSONB DEFAULT '{}'::jsonb,
    enable_config VARCHAR(10)[] DEFAULT '{}'
);
ALTER TABLE public.chip_exti
ADD COLUMN name VARCHAR(100),  -- 名称
ADD COLUMN des TEXT;           -- 描述