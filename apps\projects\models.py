import datetime
import json

from django.db import models
from django.db import transaction

from utils.sql_helper import sql_execute, sql_fetchall_dict, sql_fetchone_dict, sql_fetchone


class ProjectModel:
    def __init__(self):
        self.table_name = "public.projects"

    def create(self, **kwargs):
        now = datetime.datetime.now()
        params = {
            "name": kwargs.get("name"),
            "number": kwargs.get("number"),
            "related_people": kwargs.get("related_people"),
            "msg_effective_time_start": kwargs.get("msg_effective_time_start"),
            "msg_effective_time_end": kwargs.get("msg_effective_time_end"),
            "create_time": now,
            "update_time": now,
        }

        sql = """
            INSERT INTO {table_name} ("name",  "number", related_people, msg_effective_time_start,
             msg_effective_time_end, create_time, update_time) 
            VALUES (%(name)s, %(number)s, %(related_people)s, %(msg_effective_time_start)s,
             %(msg_effective_time_end)s, %(create_time)s, %(update_time)s)
             ON CONFLICT ( "number" ) DO UPDATE
             SET "name" = excluded.name, "number" = excluded.number, related_people = excluded.related_people,
             msg_effective_time_start = excluded.msg_effective_time_start, 
             msg_effective_time_end = excluded.msg_effective_time_end,
             update_time = excluded.update_time
             ;
        """.format(table_name=self.table_name)

        sql_execute(sql, params)

    def config(self, **kwargs):

        configs = kwargs.get("configs")
        if kwargs.get("dbc_path"):
            configs["dbc_path"] = kwargs.get("dbc_path")

        now = datetime.datetime.now()
        params = {
            "name": kwargs.get("name"),
            "number": kwargs.get("number"),
            "configs": json.dumps(configs),
            "create_time": now,
            "update_time": now,
        }

        sql = """
            INSERT INTO {table_name} ("name",  "number", configs, create_time, update_time) 
            VALUES (%(name)s, %(number)s, %(configs)s, %(create_time)s, %(update_time)s)
             ON CONFLICT ( "number" ) DO UPDATE
             SET "name" = excluded.name, "number" = excluded.number, 
             configs = excluded.configs,
             update_time = excluded.update_time
             ;
        """.format(table_name=self.table_name)

        sql_execute(sql, params)

    @staticmethod
    def _build_sql_where(**kwargs):
        name = kwargs.get("name")
        number = kwargs.get("number")

        params = {
        }

        sql_where_list = []

        if name is not None and name != '':
            sql_where_list.append("name = %(name)s")
            params["name"] = name

        if number is not None and number != '':
            sql_where_list.append("number = %(number)s")
            params["number"] = number

        if sql_where_list:
            sql_where = " WHERE " + " AND ".join(sql_where_list)
        else:
            sql_where = ""

        return sql_where, params

    def count(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT count(*)
                FROM {table_name}
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone_dict(sql, params)

        return result

    def exists(self, **kwargs):
        sql_where, params = self._build_sql_where(**kwargs)

        sql = """
            SELECT id
                FROM {table_name}
                {sql_where}
                LIMIT 1;
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )

        result = sql_fetchone(sql, params)

        return result is not None

    def list(self, **kwargs):
        page = kwargs.get("page", 1)
        pagesize = kwargs.get("pagesize", 10)

        sql_where, params = self._build_sql_where(**kwargs)

        sql_order_by = " ORDER BY id "

        sql_limit = " LIMIT {} OFFSET {} ".format(pagesize, (page - 1) * pagesize)

        sql = """
            SELECT count(*)
                FROM {table_name} 
                {sql_where}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where
        )
        content = {
            "count": sql_fetchone_dict(sql, params).get("count")
        }

        sql = """
            SELECT id, "name", "number", "related_people", msg_effective_time_start, msg_effective_time_end, 
                create_time, update_time
             FROM {table_name}
            {sql_where}
            {sql_order_by}
            {sql_limit}
            ;
        """.format(
            table_name=self.table_name,
            sql_where=sql_where,
            sql_order_by=sql_order_by,
            sql_limit=sql_limit,
        )

        content["results"] = sql_fetchall_dict(sql, params)

        return content

    def retrieve(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            SELECT id, "name", "number", "related_people", msg_effective_time_start, msg_effective_time_end, 
                configs, create_time, update_time
                FROM {table_name}
                WHERE id = %(id)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        return result

    def retrieve_by_number(self, number):
        params = {
            "number": number,
        }
        sql = """
            SELECT id, "name", "number", "related_people", msg_effective_time_start, msg_effective_time_end, configs,
                create_time, update_time
                FROM {table_name}
                WHERE "number" = %(number)s
                LIMIT 1;
        """.format(table_name=self.table_name)

        result = sql_fetchone_dict(sql, params)

        if result:
            result["related_people"] = json.loads(result["related_people"]) if result["related_people"] else []
            result["configs"] = json.loads(result["configs"]) if result["configs"] else {}

        return result

    def delete(self, pk):
        params = {
            "id": pk,
        }
        sql = """
            DELETE FROM {table_name}
                WHERE id = %(id)s;
        """.format(table_name=self.table_name)

        result = sql_execute(sql, params)

        return result

    def update(self, **kwargs):
        params = {
            "id": kwargs.get("pk"),
            "update_time": datetime.datetime.now(),
        }

        sql_set_list = []

        name = kwargs.get("name")
        if name is not None:
            sql_set_list.append("name = %(name)s")
            params["name"] = name

        number = kwargs.get("number")
        if number is not None:
            sql_set_list.append("number = %(number)s")
            params["number"] = number

        related_people = kwargs.get("related_people")
        if related_people is not None:
            sql_set_list.append("related_people = %(related_people)s")
            params["related_people"] = related_people

        msg_effective_time_start = kwargs.get("msg_effective_time_start")
        if msg_effective_time_start is not None:
            if msg_effective_time_start == "":
                msg_effective_time_start = None
            sql_set_list.append("msg_effective_time_start = %(msg_effective_time_start)s")
            params["msg_effective_time_start"] = msg_effective_time_start

        msg_effective_time_end = kwargs.get("msg_effective_time_end")
        if msg_effective_time_end is not None:
            if msg_effective_time_end == "":
                msg_effective_time_end = None
            sql_set_list.append("msg_effective_time_end = %(msg_effective_time_end)s")
            params["msg_effective_time_end"] = msg_effective_time_end

        if not sql_set_list:
            return

        sql_set = ", ".join(sql_set_list)

        sql = """
            UPDATE {table_name}     
                SET 
                {sql_set},
                update_time = %(update_time)s
                WHERE id = %(id)s;
        """.format(
            table_name=self.table_name,
            sql_set=sql_set,
        )

        result = sql_execute(sql, params)

        return result


def get_test_case_stats(project_number):
    stats = {}

    sql = """
        SELECT f."name", f."number"
            FROM public.functions2 f right join public.project_module_map pmm
            on f.id = pmm.module_id
            WHERE pmm.project_number = %(project_number)s and level = 1
    """
    r = sql_fetchall_dict(sql, {"project_number": project_number})
    module_map = {}
    for i in r:
        module_map[i["number"]] = i["name"]

    action_type_map = {
        "SAT": "系统合格性测试",
        "IST": "系统集成测试",
        "SQT": "软件合格性测试",
        "SIT": "软件集成测试",
        "SUT": "软件单元测试",
        "HT": "硬件测试",
    }

    sql = """
        select execute_mode, count(id)
        from public.test_cases2
        where "project_number" = %(project_number)s and not(is_deleted)
        group by execute_mode
        ;
    """
    r = sql_fetchall_dict(sql, {"project_number": project_number})

    stats["AUTOMATED_EXECUTION"] = 0
    stats["MANUAL_EXECUTION"] = 0
    stats["SEMI_AUTOMATED_EXECUTION"] = 0

    for i in r:
        stats[i["execute_mode"]] = i["count"]

    stats["total"] = stats["AUTOMATED_EXECUTION"] + stats["MANUAL_EXECUTION"] + stats["SEMI_AUTOMATED_EXECUTION"]

    stats["auto_rate"] = round(((stats["AUTOMATED_EXECUTION"] + stats["SEMI_AUTOMATED_EXECUTION"]) / stats["total"]
                                if stats["total"] else 0), 2)

    stats["auto_pie"] = [
        {
            "value": stats["AUTOMATED_EXECUTION"],
            "name": "自动化测试用例"
        },
        {
            "value": stats["SEMI_AUTOMATED_EXECUTION"],
            "name": "半自动化测试用例"
        },
        {
            "value": stats["MANUAL_EXECUTION"],
            "name": "手动测试用例"
        },
    ]

    sql = """
        select action_type, execute_mode, count(id)
        from public.test_cases2
        where "project_number" = %(project_number)s and not(is_deleted)
        group by action_type, execute_mode
    """
    r = sql_fetchall_dict(sql, {"project_number": project_number})

    at_d = {}
    for action_type in action_type_map:
        at_d[action_type] = {
            "AUTOMATED_EXECUTION": 0,
            "MANUAL_EXECUTION": 0,
            "SEMI_AUTOMATED_EXECUTION": 0,
        }
    for i in r:
        at_d[i["action_type"]][i["execute_mode"]] = i["count"]

    stats["action_type_bar"] = {
        "labels": [action_type_map[i] for i in at_d],
        "data": [
            {
                "name": "自动化测试用例",
                "data": [at_d[i]["AUTOMATED_EXECUTION"] for i in at_d]
            },
            {
                "name": "半自动测试用例",
                "data": [at_d[i]["SEMI_AUTOMATED_EXECUTION"] for i in at_d]
            },
            {
                "name": "手动测试用例",
                "data": [at_d[i]["MANUAL_EXECUTION"] for i in at_d]
            },
        ]
    }

    sql = """
        select "module", execute_mode, count(id)
            from public.test_cases2
            where "project_number" = %(project_number)s and not(is_deleted)
            group by "module", execute_mode
    ;
    """
    r = sql_fetchall_dict(sql, {"project_number": project_number})

    m_d = {}
    for i in module_map:
        m_d[i] = {
            "module_name": module_map[i],
            "AUTOMATED_EXECUTION": 0,
            "MANUAL_EXECUTION": 0,
            "SEMI_AUTOMATED_EXECUTION": 0,
        }
    for i in r:
        if m_d.get(i["module"]) is not None:
            m_d[i["module"]][i["execute_mode"]] = i["count"]

    stats["module_bar"] = {
        "labels": [m_d[i]["module_name"] for i in m_d],
        "data": [
            {
                "name": "自动化测试用例",
                "data": [m_d[i]["AUTOMATED_EXECUTION"] for i in m_d]
            },
            {
                "name": "半自动测试用例",
                "data": [m_d[i]["SEMI_AUTOMATED_EXECUTION"] for i in m_d]
            },
            {
                "name": "手动测试用例",
                "data": [m_d[i]["MANUAL_EXECUTION"] for i in m_d]
            },
        ]

    }

    sql = """
        select content, stats_time
            from public.test_case_stats_weekly
            order by stats_time desc 
            limit 4
        ;
    """
    r = sql_fetchall_dict(sql)
    r.reverse()

    stats_time_list = [str(k.get("stats_time")) for k in r]
    at_m_line_data = {}
    for i in action_type_map:
        at_m_line_data[i] = {}
        for j in stats_time_list:
            at_m_line_data[i][j] = {
                "AUTOMATED_EXECUTION": 0,
                "MANUAL_EXECUTION": 0,
                "SEMI_AUTOMATED_EXECUTION": 0,
            }

    for record in r:
        content = json.loads(record.get("content"))
        content = content.get(project_number, [])
        stats_time = str(record.get("stats_time"))

        for i in content:
            try:
                at_m_line_data[i["action_type"]][stats_time][i["execute_mode"]] += i["count"]
            except Exception:
                pass

    for i in at_m_line_data:
        for j in at_m_line_data[i]:
            total = (at_m_line_data[i][j]["AUTOMATED_EXECUTION"] +
                     at_m_line_data[i][j]["SEMI_AUTOMATED_EXECUTION"] +
                     at_m_line_data[i][j]["MANUAL_EXECUTION"])
            if total:
                at_m_line_data[i][j]["auto_rate"] = round(
                    (at_m_line_data[i][j]["AUTOMATED_EXECUTION"] +
                     at_m_line_data[i][j]["SEMI_AUTOMATED_EXECUTION"]) / total, 2
                )
            else:
                at_m_line_data[i][j]["auto_rate"] = 0

    stats["at_m_line"] = {
        "labels": stats_time_list,
        "data": [
            {
                "data": [at_m_line_data[i][j]["auto_rate"] for j in stats_time_list],
                "name": action_type_map[i],
            } for i in action_type_map
        ]
    }

    sql = """
        select action_type, operate_method, count(id)
        from public.test_case_operation_log
        where operate_time >= %(start_time)s and operate_time < %(end_time)s and project_number = %(project_number)s
        group by action_type, operate_method
        ;
    """
    today = datetime.date.today()
    start_time = today - datetime.timedelta(days=today.weekday())
    end_time = start_time + datetime.timedelta(days=7)
    r = sql_fetchall_dict(sql, {"project_number": project_number, "start_time": start_time, "end_time": end_time})

    operate_method_map = {
        "CREATE": "新增",
        "UPDATE": "修改",
        "DELETE": "删除",
        "REVIEW": "发起评审",
        "REVIEWED": "评审通过",
    }

    operate_bar_data = {}
    for i in action_type_map:
        operate_bar_data[i] = {}
        for j in operate_method_map:
            operate_bar_data[i][j] = 0
    for i in r:
        try:
            operate_bar_data[i["action_type"]][i["operate_method"]] = i["count"]
        except KeyError:
            pass

    stats["operate_bar"] = {
        "start_time": start_time,
        "end_time": end_time - datetime.timedelta(days=1),
        "labels": [action_type_map[i] for i in action_type_map],
        "data": [
            {
                "data": [operate_bar_data[j][i] for j in action_type_map],
                "name": operate_method_map[i],
            } for i in operate_method_map
        ]
    }

    return stats


def get_test_case_action_type_module_stats(project_number, action_type=None):
    params = {"project_number": project_number}
    action_type_where = " "
    if action_type:
        action_type_where = " and action_type = %(action_type)s "
        params["action_type"] = action_type

    sql = """
            SELECT f."name", f."number"
                FROM public.functions2 f right join public.project_module_map pmm
                on f.id = pmm.module_id
                WHERE pmm.project_number = %(project_number)s and f.level = 1
                order by f.id
        """
    r = sql_fetchall_dict(sql, params)
    module_map = {}
    for i in r:
        module_map[i["number"]] = i["name"]

    sql = """
         select "module", execute_mode, count(id)
            from public.test_cases2
            where "project_number" = %(project_number)s and not(is_deleted)
            {action_type_where}
            group by "module", execute_mode
    ;
    """.format(action_type_where=action_type_where)
    r = sql_fetchall_dict(sql, params)

    m_d = {}
    for i in module_map:
        m_d[i] = {
            "module_name": module_map[i],
            "AUTOMATED_EXECUTION": 0,
            "MANUAL_EXECUTION": 0,
            "SEMI_AUTOMATED_EXECUTION": 0,
        }
    for i in r:
        if m_d.get(i["module"]) is not None:
            m_d[i["module"]][i["execute_mode"]] = i["count"]

    stats = {
        "labels": [m_d[i]["module_name"] for i in m_d],
        "data": [
            {
                "name": "自动化测试用例",
                "data": [m_d[i]["AUTOMATED_EXECUTION"] for i in m_d]
            },
            {
                "name": "半自动测试用例",
                "data": [m_d[i]["SEMI_AUTOMATED_EXECUTION"] for i in m_d]
            },
            {
                "name": "手动测试用例",
                "data": [m_d[i]["MANUAL_EXECUTION"] for i in m_d]
            },
        ]

    }

    return stats


class ProjectModuleMap(models.Model):
    project_number = models.CharField(max_length=255)
    module_id = models.IntegerField()

    class Meta:
        db_table = 'project_module_map'
        managed = False
        app_label = 'projects'


class ProjectModulesModel:

    @classmethod
    def parse_functions(cls, functions, parent_id=None, parent_number=""):
        results = []
        tmp = [i for i in functions]

        for f in tmp:
            if f["parent_id"] == parent_id or (parent_id is None and f["parent_id"] is None):
                if parent_number:
                    f["number2"] = parent_number + "," + f["number"]
                else:
                    f["number2"] = f["number"]
                children = cls.parse_functions(functions, parent_id=f["id"], parent_number=f["number2"])
                if children:
                    f["children"] = children
                results.append(f)

        return results

    @classmethod
    def list(cls, **kwargs):
        project_number = kwargs.get("project_number")

        sql = """
            SELECT f.id, f."name", f."number", f."desc", f.parent_id, f."level", f.create_time, f.update_time, f.deprecated
                FROM public.functions2 f right join public.project_module_map pmm
                on f.id = pmm.module_id
                WHERE pmm.project_number = %(project_number)s
                ORDER BY id
            ;
        """
        results = sql_fetchall_dict(sql, {"project_number": project_number})

        content = {
            "results": cls.parse_functions(results),
            "ids": [i.get("id") for i in results]
        }

        return content

    @classmethod
    def create(cls, **kwargs):
        project_number = kwargs.get("project_number")
        module_ids = kwargs.get("module_ids")

        with transaction.atomic():
            objs = ProjectModuleMap.objects.filter(project_number=project_number)
            objs.delete()

            for module_id in module_ids:
                ProjectModuleMap.objects.create(project_number=project_number, module_id=module_id)


class ProjectInspectionItemMap(models.Model):
    project_number = models.CharField(max_length=255)
    item_id = models.IntegerField()

    class Meta:
        db_table = 'project_inspection_item_map'
        managed = False
        app_label = 'projects'


class ProjectInspectionItemModel:

    @classmethod
    def list(cls, **kwargs):
        project_number = kwargs.get("project_number")

        sql = """
            SELECT i.id, i.parent_id, i.path, i."level", i."name", i.code, i."desc", i.type
                FROM public.inspection_items i right join public.project_inspection_item_map pim
                on i.id = pim.item_id
                WHERE pim.project_number = %(project_number)s
                ORDER BY i.level, i.id
            ;
        """
        results = sql_fetchall_dict(sql, {"project_number": project_number})

        data_dict = {}
        for item in results:
            data_dict[item.get("id")] = item
            item["children"] = []

        data_tree = []
        for item_id, item in data_dict.items():
            if item["parent_id"] == 0:
                data_tree.append(item)
            elif item["parent_id"] in data_dict:
                data_dict[item["parent_id"]]["children"].append(item)

        content = {
            "results": data_tree,
            "ids": [i.get("id") for i in results]
        }

        return content

    @classmethod
    def create(cls, **kwargs):
        project_number = kwargs.get("project_number")
        item_ids = kwargs.get("item_ids")

        with transaction.atomic():
            objs = ProjectInspectionItemMap.objects.filter(project_number=project_number)
            objs.delete()

            for item_id in item_ids:
                ProjectInspectionItemMap.objects.create(project_number=project_number, item_id=item_id)


def get_product_version_tr(project_number):
    sql = """
        select COUNT(*) as total,
            COALESCE(
            SUM(
                CASE 
                    WHEN status = 'DEPRECATED' THEN 1
                    ELSE 0 
                END
                ), 0
            ) AS deprecated
        from public.product_versions
        where project_number = %(project_number)s
    """
    result = sql_fetchone_dict(sql, {"project_number": project_number})

    return result
