CREATE TABLE public.chip_gpio (
    id SERIAL PRIMARY KEY,
    type VARCHAR(20)[] DEFAULT '{}',               -- 类型
    work_model VARCHAR(20)[] DEFAULT '{}',         -- 工作模式
    speed VARCHAR(20)[] DEFAULT '{}',              -- 速度
    output_type VARCHAR(20)[] DEFAULT '{}',        -- 输出类型
    output_level VARCHAR(10)[] DEFAULT '{}',       -- 默认电平
    up_down_pullType VARCHAR(10)[] DEFAULT '{}',   -- 上下拉选择
    reversal VARCHAR(10)[] DEFAULT '{}'            -- 是否翻转
);
ALTER TABLE public.chip_gpio
ADD COLUMN name VARCHAR(100),  -- 名称
ADD COLUMN des TEXT;           -- 描述