# routers.py

class AppDatabaseRouter:

    def db_for_read(self, model, **hints):
        """
        指明读取数据时使用的数据库
        """
        if model._meta.app_label == 'code_project_info':
            return 'default'
        elif model._meta.app_label == 'code_branch_info':
            return 'default'
        elif model._meta.app_label == 'chip':
            return 'hwcp'
        elif model._meta.app_label == 'chip_color':
            return 'hwcp'
        elif model._meta.app_label == 'chip_pin_config':
            return 'hwcp'
        elif model._meta.app_label == 'chip_gpio':
            return 'hwcp'
        elif model._meta.app_label == 'chip_adc':
            return 'hwcp'
        elif model._meta.app_label == 'chip_pwm':
            return 'hwcp'
        elif model._meta.app_label == 'chip_iic':
            return 'hwcp'
        elif model._meta.app_label == 'chip_spi':
            return 'hwcp'
        elif model._meta.app_label == 'chip_uart':
            return 'hwcp'
        elif model._meta.app_label == 'chip_exti':
            return 'hwcp'
        elif model._meta.app_label == 'chip_map':
            return 'hwcp'
        return None

    def db_for_write(self, model, **hints):
        """
        指明写入数据时使用的数据库
        """
        if model._meta.app_label == 'code_project_info':
            return 'default'
        elif model._meta.app_label == 'code_branch_info':
            return 'default'
        elif model._meta.app_label == 'chip':
            return 'hwcp'
        elif model._meta.app_label == 'chip_color':
            return 'hwcp'
        elif model._meta.app_label == 'chip_pin_config':
            return 'hwcp'
        elif model._meta.app_label == 'chip_gpio':
            return 'hwcp'
        elif model._meta.app_label == 'chip_adc':
            return 'hwcp'
        elif model._meta.app_label == 'chip_pwm':
            return 'hwcp'
        elif model._meta.app_label == 'chip_iic':
            return 'hwcp'
        elif model._meta.app_label == 'chip_spi':
            return 'hwcp'
        elif model._meta.app_label == 'chip_uart':
            return 'hwcp'
        elif model._meta.app_label == 'chip_exti':
            return 'hwcp'
        elif model._meta.app_label == 'chip_map':
            return 'hwcp'
        return None


    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """
        控制哪些应用的模型可以迁移到指定数据库
        """
        if app_label == 'code_project_info':
            return db == 'default'
        elif app_label == 'code_branch_info':
            return db == 'default'
        elif app_label == 'chip':
            return db == 'hwcp'
        elif app_label == 'chip_color':
            return db == 'hwcp'
        elif app_label == 'chip_pin_config':
            return db == 'hwcp'
        elif app_label == 'chip_gpio':
            return db == 'hwcp'
        elif app_label == 'chip_adc':
            return db == 'hwcp'
        elif app_label == 'chip_pwm':
            return db == 'hwcp'
        elif app_label == 'chip_iic':
            return db == 'hwcp'
        elif app_label == 'chip_spi':
            return db == 'hwcp'
        elif app_label == 'chip_uart':
            return db == 'hwcp'
        elif app_label == 'chip_exti':
            return db == 'hwcp'
        elif app_label == 'chip_map':
            return db == 'hwcp'
        return None