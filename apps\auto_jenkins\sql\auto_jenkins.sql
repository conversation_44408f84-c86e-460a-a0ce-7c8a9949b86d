--
-- Pipeline项目配置表
--
CREATE TABLE public.pipeline_projects (
    id SERIAL PRIMARY KEY,
    project_name VARCHAR(100) UNIQUE NOT NULL,
    git_url VARCHAR(200) NOT NULL,
    engineering_group VARCHAR(50) NOT NULL,
    engineering_path VARCHAR(200) NOT NULL,
    agent <PERSON><PERSON>HAR(50) DEFAULT 'slave_win10' NOT NULL,
    pipeline_config JSONB NOT NULL,
    jenkinsfile_content TEXT NOT NULL,
    jenkins_job_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
    creator <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL
);