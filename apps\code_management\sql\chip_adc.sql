CREATE TABLE public.chip_adc (
    id SERIAL PRIMARY KEY,
    type VARCHAR(20)[] DEFAULT '{}',               -- 类型
    work_model VARCHAR(20)[] DEFAULT '{}',         -- 工作模式
    alt_value VARCHAR(50),                        -- 复用值
    channel VARCHAR(50),                           -- 通道号
    sampling_period VARCHAR(10)[] DEFAULT '{}',    -- 采样周期（ms）
    enable_config VARCHAR(10)[] DEFAULT '{}'       -- 是否启用配置
);


ALTER TABLE public.chip_adc
ADD COLUMN name VARCHAR(100),  -- 名称
ADD COLUMN des TEXT;           -- 描述