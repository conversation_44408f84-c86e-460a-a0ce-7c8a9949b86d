

from django.db import models
from rest_framework import serializers
from django.contrib.postgres.fields import ArrayField

#
# 模型定义
#

class CodeProjectInfo(models.Model):
    id = models.AutoField(primary_key=True)
    project_code = models.CharField(max_length=50, verbose_name='项目代号')
    project_name = models.CharField(max_length=120, verbose_name='项目名称')
    sdk_version = models.CharField(max_length=50, blank=True, null=True, verbose_name='sdk 分支/标签信息')
    project_gitlab = models.CharField(max_length=255, primary_key=True, verbose_name='GitLab 地址')
    chip = models.CharField(max_length=120, blank=True, null=True, verbose_name='芯片类型')
    modules = ArrayField(models.JSONField(blank=True, null=True, verbose_name='功能模块'), default=list)
    project_space = models.Cha<PERSON><PERSON><PERSON>(max_length=255, blank=True, null=True, verbose_name='项目路径')
    project_group = models.CharField(max_length=120, blank=True, null=True, verbose_name='所属组')
    project_description = models.CharField(max_length=255, blank=True, null=True, verbose_name='项目描述')
    version_rule = models.CharField(max_length=255, blank=True, null=True, verbose_name='版本策略')
    create_person = models.CharField(max_length=120, blank=True, null=True, verbose_name='创建人')


    class Meta:
        db_table = 'code_project_info'
        app_label = 'code_project_info'
        managed = False



class ProjectInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = CodeProjectInfo
        fields = '__all__'



# 序列化器定义
class CodeBranchInfo(models.Model):

    id = models.AutoField(primary_key=True)
    project_code = models.CharField(max_length=50, verbose_name='项目代号')
    project_name = models.CharField(max_length=120, verbose_name='项目名称')
    project_gitlab = models.CharField(max_length=255, verbose_name='GitLab 地址')
    project_branch = models.CharField(max_length=255, verbose_name='分支名称')
    chip = models.CharField(max_length=120, blank=True, null=True, verbose_name='芯片类型')
    modules = ArrayField(models.JSONField(blank=True, null=True, verbose_name='功能模块'), default=list)
    branch_space = models.CharField(max_length=255, blank=True, null=True, verbose_name='分支空间')
    project_group = models.CharField(max_length=120, blank=True, null=True, verbose_name='所属组')
    create_person = models.CharField(max_length=120, blank=True, null=True, verbose_name='创建人')
    mr_iid = models.IntegerField(blank=True, null=True, verbose_name='merge_id')
    class Meta:
        db_table = 'code_branch_info'
        app_label = 'code_branch_info'
        managed = False



class BranchInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = CodeBranchInfo
        fields = '__all__'




# 芯片引脚配置
class Chip(models.Model):
    pin_id = models.IntegerField(verbose_name="引脚号")  # 示例: 5
    pin_name = models.CharField(max_length=20, verbose_name="引脚名称")  # 示例: PH_8
    chip = models.CharField(max_length=50, verbose_name="芯片名")  # 示例: KF32A158
    model = models.CharField(max_length=20, verbose_name="当前配置类型")  # 示例: PWM
    module = ArrayField(  # 示例: ["PWM", "GPIO"]
        base_field=models.CharField(max_length=20),
        verbose_name="功能类型"
    )
    alt_value = models.JSONField(null=True, blank=True, verbose_name="复用值")
    status = models.BooleanField(default=False, verbose_name="配置状态")  # 示例: False

    class Meta:
        db_table = 'chip'
        app_label = 'chip'
        managed = False

class ChipSerializer(serializers.ModelSerializer):
    class Meta:
        model = Chip
        fields = '__all__'




# 功能模块颜色
class ChipColor(models.Model):
    module = models.CharField(max_length=50, verbose_name="模块")         # 示例: GPIO
    color = models.CharField(max_length=7, verbose_name="引脚颜色")        # 示例: #A5D6A7
    
    class Meta:
        db_table = 'chip_color'
        app_label = 'chip_color'
        managed = False

class ChipColorSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChipColor
        fields = '__all__'



# 芯片引脚默认值
class ChipPinConfig(models.Model):
    pin_id = models.IntegerField(verbose_name="引脚号")  # 示例: 47
    chip = models.CharField(max_length=50, verbose_name="芯片名")  # 示例: KF32A158
    module = models.CharField(max_length=50, verbose_name="当前功能类型配置")  # 示例: UART
    name = models.CharField(max_length=100, verbose_name="通道名称")  # 示例: Debug_1
    des = models.CharField(max_length=255, verbose_name="引脚注释")  # 示例: 调试
    func = models.JSONField(verbose_name="当前配置类型")  # 示例: {"type":"","work_model":"","alt_value":""}
    config = models.JSONField(verbose_name="当前配置")  # 示例: {"channel":"","communication_speed":"","interupt_flag":"","enable_config":""}

    class Meta:
        db_table = 'chip_pin_config'
        app_label = 'chip_pin_config'
        managed = False

class ChipPinConfigSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChipPinConfig
        fields = '__all__'




# GPIO配置规则
class ChipGPIO(models.Model):
    name = models.CharField(
        max_length=100,
        verbose_name="名称",
        blank=True,
        default=""
    )
    des = models.TextField(
        verbose_name="描述",
        blank=True,
        default=""
    )
    type = ArrayField(
        base_field=models.CharField(max_length=20),
        verbose_name="类型",  # 示例: ["GPIO"]
        default=list
    )
    work_model = ArrayField(
        base_field=models.CharField(max_length=20),
        verbose_name="工作模式",  # 示例: ["输入", "输出", "复用", "模拟"]
        default=list
    )
    speed = ArrayField(
        base_field=models.CharField(max_length=20),
        verbose_name="速度",  # 示例: ["高速", "中速", "低速"]
        default=list
    )
    output_type = ArrayField(
        base_field=models.CharField(max_length=20),
        verbose_name="输出类型",  # 示例: ["开漏", "推挽"]
        default=list
    )
    output_level = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="默认电平",  # 示例: ["低", "高"]
        default=list
    )
    up_down_pulltype = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="上下拉选择",  # 示例: ["是", "否"]
        default=list
    )
    reversal = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="是否翻转",  # 示例: ["true", "false"]
        default=list
    )
    enable_config = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="是否启用配置",
        default=list
    )
    class Meta:
        db_table = 'chip_gpio'
        app_label = 'chip_gpio'
        managed = False

class ChipGpioSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChipGPIO
        fields = '__all__'





class ChipADC(models.Model):
    name = models.CharField(
        max_length=100,
        verbose_name="名称",
        blank=True,
        default=""
    )
    des = models.TextField(
        verbose_name="描述",
        blank=True,
        default=""
    )
    type = ArrayField(
        base_field=models.CharField(max_length=20),
        verbose_name="类型",
        default=list
    )
    work_model = ArrayField(
        base_field=models.CharField(max_length=20),
        verbose_name="工作模式",
        default=list
    )
    alt_value = models.CharField(
        max_length=50,
        verbose_name="复用值",
        blank=True
    )
    channel = models.CharField(
        max_length=50,
        verbose_name="通道号",
        blank=True
    )
    sampling_period = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="采样周期（ms）",
        default=list
    )
    enable_config = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="是否启用配置",
        default=list
    )

    class Meta:
        db_table = 'chip_adc'
        app_label = 'chip_adc'
        managed = False

class ChipADCSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChipADC
        fields = '__all__'   



class ChipPWM(models.Model):
    name = models.CharField(
        max_length=100,
        verbose_name="名称",
        blank=True,
        default=""
    )
    des = models.TextField(
        verbose_name="描述",
        blank=True,
        default=""
    )
    type = ArrayField(
        base_field=models.CharField(max_length=20),
        verbose_name="类型",
        default=list
    )
    work_model = ArrayField(
        base_field=models.CharField(max_length=20),
        verbose_name="工作模式",
        default=list
    )
    alt_value = models.CharField(
        max_length=50,
        verbose_name="复用值",
        blank=True
    )
    channel = models.CharField(
        max_length=50,
        verbose_name="通道号",
        blank=True
    )
    clock_division = ArrayField(
        base_field=models.CharField(max_length=5),
        verbose_name="时钟分频",
        default=list
    )
    output_frequency = models.CharField(
        max_length=50,
        verbose_name="输出频率",
        blank=True
    )
    duty_cycle = models.JSONField(
        verbose_name="占空比 [%]",
        default=dict
    )
    enable_config = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="是否启用配置",
        default=list
    )

    class Meta:
        db_table = 'chip_pwm'
        app_label = 'chip_pwm'
        managed = False

class ChipPWMSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChipPWM
        fields = '__all__'   




class ChipIIC(models.Model):
    name = models.CharField(
        max_length=100,
        verbose_name="名称",
        blank=True,
        default=""
    )
    des = models.TextField(
        verbose_name="描述",
        blank=True,
        default=""
    )
    type = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="类型",
        default=list
    )
    func_work_model = ArrayField(
        base_field=models.CharField(max_length=20),
        verbose_name="工作模式",
        default=list
    )
    func_alt_value = models.CharField(
        max_length=50,
        verbose_name="复用值",
        blank=True
    )
    iic_type = ArrayField(
        base_field=models.CharField(max_length=20),
        verbose_name="IIC 类型",
        default=list
    )
    channel = models.CharField(
        max_length=50,
        verbose_name="通道号",
        blank=True
    )
    communication_speed = models.CharField(
        max_length=50,
        verbose_name="通讯速率[kHz]",
        blank=True
    )
    device_address = models.CharField(
        max_length=50,
        verbose_name="设备地址（8bit）",
        blank=True
    )
    work_mode = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="工作模式",
        default=list
    )
    sub_addr_type = ArrayField(
        base_field=models.CharField(max_length=50),
        verbose_name="设置子地址类型",
        default=list
    )
    sub_addr_byte = models.JSONField(
        verbose_name="设备子地址字节数",
        default=dict
    )
    enable_config = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="是否启用配置",
        default=list
    )
    
    class Meta:
        db_table = 'chip_iic'
        app_label = 'chip_iic'
        managed = False

class ChipIICSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChipIIC
        fields = '__all__'   





class ChipSPI(models.Model):
    name = models.CharField(
        max_length=100,
        verbose_name="名称",
        blank=True,
        default=""
    )
    des = models.TextField(
        verbose_name="描述",
        blank=True,
        default=""
    )
    type = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="类型",
        default=list
    )
    func_work_model = ArrayField(
        base_field=models.CharField(max_length=20),
        verbose_name="工作模式",
        default=list
    )
    func_alt_value = models.CharField(
        max_length=50,
        verbose_name="复用值",
        blank=True
    )
    spi_type = ArrayField(
        base_field=models.CharField(max_length=20),
        verbose_name="SPI 类型",
        default=list
    )
    soft_spi_type = ArrayField(
        base_field=models.CharField(max_length=20),
        verbose_name="软件 SPI 类型",
        default=list
    )
    channel = models.CharField(
        max_length=50,
        verbose_name="通道号",
        blank=True
    )
    communication_speed = models.CharField(
        max_length=50,
        verbose_name="通讯速率[kHz]",
        default="500"
    )
    transfor_bits = ArrayField(
        base_field=models.CharField(max_length=5),
        verbose_name="传输位数[bits]",
        default=list
    )
    work_mode = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="工作模式",
        default=list
    )
    enable_config = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="是否启用配置",
        default=list
    )

    class Meta:
        db_table = 'chip_spi'
        app_label = 'chip_spi'
        managed = False

class ChipSPISerializer(serializers.ModelSerializer):
    class Meta:
        model = ChipSPI
        fields = '__all__'   





class ChipUART(models.Model):
    name = models.CharField(
        max_length=100,
        verbose_name="名称",
        blank=True,
        default=""
    )
    des = models.TextField(
        verbose_name="描述",
        blank=True,
        default=""
    )
    type = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="类型",
        default=list
    )
    func_work_model = ArrayField(
        base_field=models.CharField(max_length=20),
        verbose_name="工作模式",
        default=list
    )
    func_alt_value = models.CharField(
        max_length=50,
        verbose_name="复用值",
        blank=True
    )
    channel = models.CharField(
        max_length=50,
        verbose_name="通道号",
        blank=True
    )
    communication_speed = ArrayField(
        base_field=models.IntegerField(),
        verbose_name="通讯速率",
        default=list
    )
    interupt_flag = ArrayField(
        base_field=models.CharField(max_length=5),
        verbose_name="中断使用",
        default=list
    )
    enable_config = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="是否启用配置",
        default=list
    )

    class Meta:
        db_table = 'chip_uart'
        app_label = 'chip_uart'
        managed = False

class ChipUARTSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChipUART
        fields = '__all__'   




class ChipEXTI(models.Model):
    name = models.CharField(
        max_length=100,
        verbose_name="名称",
        blank=True,
        default=""
    )
    des = models.TextField(
        verbose_name="描述",
        blank=True,
        default=""
    )
    type = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="类型",
        default=list
    )
    alt_value = models.CharField(
        max_length=50,
        verbose_name="复用值",
        blank=True
    )
    work_model = ArrayField(
        base_field=models.CharField(max_length=20),
        verbose_name="工作模式",
        default=list
    )
    speed = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="速度",
        default=list
    )
    up_down_pulltype = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="上下拉选择",
        default=list
    )
    channel = ArrayField(
        base_field=models.CharField(max_length=5),
        verbose_name="通道号",
        default=list
    )
    preemptpriority = models.JSONField(
        verbose_name="抢占优先级",
        default=dict
    )
    subpriority = models.JSONField(
        verbose_name="响应优先级",
        default=dict
    )
    enable_config = ArrayField(
        base_field=models.CharField(max_length=10),
        verbose_name="是否启用配置",
        default=list
    )

    class Meta:
        db_table = 'chip_exti'
        app_label = 'chip_exti'
        managed = False

class ChipEXTISerializer(serializers.ModelSerializer):
    class Meta:
        model = ChipEXTI
        fields = '__all__'   




class ChipMap(models.Model):
    name = models.CharField(
        max_length=50,
        verbose_name="引脚号"  # 如：PA0、PB1
    )
    label = models.CharField(
        max_length=100,
        verbose_name="UI界面标签"  # 用于 UI 显示，如 “PWM输出口”
    )

    class Meta:
        db_table = 'chip_map'
        app_label = 'chip_map'
        managed = False

class ChipMapSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChipMap
        fields = '__all__'   

