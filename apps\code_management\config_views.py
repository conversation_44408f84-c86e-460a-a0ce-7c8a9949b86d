import logging
import traceback
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from django.db import transaction
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.cache import cache
from django.core.serializers import serialize
import json
import os
import shutil
import time
from pathlib import Path

logger = logging.getLogger("code_management")
from apps.code_management.grpc.host_machine_grpc import host_machine_client
from apps.code_management.grpc.git_grpc import git_client
from .models import ProjectInfoSerializer, CodeProjectInfo, CodeBranchInfo, BranchInfoSerializer
from users.models import UserFSInfo, User
from utils.fs_service import FSService
from utils.fs_app import fs_app
from .models import ProjectInfoSerializer, CodeProjectInfo, CodeBranchInfo, BranchInfoSerializer

# local_path = "/home/<USER>/hwcp"
local_path = "F:\project\HWCP"
config_path = "/home/<USER>/hwcp/gRPC_Server/HwcpTest"
config_url = "http://*********/python-team/HwcpTest.git"
config_branch = "main"
sdk_git_path = "http://*********/"


def get_user_email(user):
    """
        获取用户详细信息
    """
    user_fs_info = UserFSInfo.objects.get(employee_number=user)
    logger.info("user_fs_info.access_token:%s", user_fs_info.access_token)
    user_info = User.objects.get(employee_number=user)
    logger.info("user_info.open_id:%s", user_info.open_id)
    fs = FSService()
    f, data = fs.get_user_info(
        token=user_fs_info.access_token,
        open_id=user_info.open_id)

    if not f:
        if data.get("message") == 401:
            raise ValueError({"err_code": 3, "msg": "平台token过期"}, status.HTTP_401_UNAUTHORIZED)
        raise ValueError({"err_code": 3, "msg": data.get("message")}, status.HTTP_500_INTERNAL_SERVER_ERROR)

    person_content = data.get("data", {})
    logger.info("person_content:", person_content)

    return person_content['username'], person_content['departmentList'][0]['leaderId']


class ConfigParamsView(APIView):

    # 提交子节点配置信息详情
    def post(self, request):
        try:
            params = request.data.get('params', {})
            logger.info("receive info: %s", params)

            config_value_path = params.get('path')
            config_value = params.get('value')
            workspace_path = params.get('workspace_path')
            branch_status = params.get('branch_status')
            # 调用grpc前，需将config_value统一成字符串
            if not isinstance(config_value, str):
                logger.info("转换 config_value %s 为字符串", config_value)
                config_value = str(config_value)
            sdk_path = os.path.dirname(workspace_path) + "/" + "hiwaysdk_2.0" + '/' + "Application"
            # 提交配置
            logger.info("send host_grpc config_path sdk_path:%s, config_value_path:%s, config_value:%s,"
                        "workspace_path:%s, branch_status:%s",
                        sdk_path, config_value_path, config_value, workspace_path, branch_status)
            response = host_machine_client.get_configParms_serve(
                sdk_path, config_value_path, config_value, workspace_path, branch_status)
            logger.info("send host_grpc currentConfigItems, get config modify status:%s", response)

            if response.config_status == 1:
                return Response({"config_status": 1, "message": "配置成功"})
            else:
                logger.info("send host_grpc currentConfigItems, get config modify fail....")
                raise ValueError("配置失败")

        except Exception as e:
            logger.info(f"配置提交失败: {str(e)}")
            return Response({"config_status": 0, "message": "Exception:" + str(e)})



class ConfigInfoView(APIView):

    def post(self, request):

        # 获取子配置信息

        try:
            params = request.data.get('params', {})
            logger.info("receive info: %s", params)
            # print("request data:", params)
            # parentName = request.query_params['parentName']
            # nodeName = request.query_params['nodeName']
            parent_name = params.get('parentName')
            node_name = params.get('nodeName')
            workspace_path = params.get('workspace_path')
            branch_status = params.get('branch_status')
            # 子项名称
            current_config_items = str(parent_name + "/" + node_name)
            sdk_path = os.path.dirname(workspace_path) + "/" + "hiwaysdk_2.0" + "/" + "Application"
            logger.info("send host_grpc currentConfigItems parmas:%s, %s, %s, %s",
                        sdk_path, current_config_items, workspace_path, branch_status)

            # 获取目录树子节点得配置项
            # 调用上位机服务
            response = host_machine_client.get_configInfo_serve(sdk_path, current_config_items, workspace_path, branch_status)
            # print("调用grpc,发送选择得项目子模块，获取子模块得配置详情：", response)
            logger.info("调用grpc currentConfigItems 服务,获取子模块得配置详情:%s", response)

            if response:
                # 嵌套多层类型数据转换
                data = json.loads(response.sub_function_items)
                logger.info("data:%s", data)

                return Response({
                    "config_status": 1,
                    "message": "配置成功",
                    "config_info": data,
                })
            else:
                logger.info("host_machine gprc get_configInfo_serve fail ....")
                raise ValueError("配置失败")
        except Exception as e:
            logger.info("Exception: %s", e)
            return Response({"config_status": 0, "message": "Exception：" + str(e)})



class ConfigCommitView(APIView):
    # 执行commit功能
    def post(self, request):
        try:
            params = request.data.get('params', {})
            logger.info("receive info:%s", params)
            commit_message = params.get('commit_message')
            workspace_path = params.get('workspace_path')
            branch_status = params.get('branch_status')

            # commit
            logger.info("当前的 workspace_path:%s", workspace_path)
            logger.info("收到commit信息:%s", commit_message)

            # 调用 git commit 服务
            commit_response = git_client.commit_serve(workspace_path, commit_message)
            logger.info("调用git, commit状态信息:%s", commit_response)

            if commit_response.success:
                return Response({"commit_status": 1, "message": "commit操作成功"})
            else:
                raise ValueError("commit操作失败: " + str(commit_response.message))

        except Exception as e:
            logger.info(f"配置提交失败: {str(e)}")
            return Response({"commit_status": 0, "message": "Exception:" + str(e)})



class ConfigPushView(APIView):
    # push功能
    def post(self, request):
        try:
            params = request.data.get('params', {})
            logger.info("receive info:%s", params)
            workspace_path = params.get('workspace_path')
            branch_status = params.get('branch_status')

            logger.info("当前的 workspace_path:%s", workspace_path)

            # push
            push_response = git_client.push_serve(workspace_path)
            logger.info("调用git, push状态信息: %s", push_response)

            if push_response.success:
                return Response({"push_status": 1, "message": "push操作成功"})

            else:
                raise ValueError(f"push操作失败{push_response.message}")

        except Exception as e:
            logger.info(f"配置提交失败: {str(e)}")
            return Response({"push_status": 0, "message": "Exception:" + str(e)})





class MergeProjectView(APIView):

    """
        merge分支
        飞书消息推送
    """

    # 人员认证
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]



    def send_leader_fs_info(self, leader_email, gitlabs, local_branchs, merge_branch, mr_url):
        """
            发送飞书消息： 给当前 分支创建人 上级 发送 merge请求
                        给当前 创建人 发送分支 已merge 得提示
        """
        # 将信息传入飞书卡片
        leader_result = False
        if leader_email:
            logger.info("即将给: %s 发送合并请求", leader_email)

            leader_result, leader_response = fs_app.send_merge_request_msg(
                leader_email,
                gitlabs.first(),
                local_branchs.first(),
                merge_branch,
                mr_url
            )


        return leader_result



    def send_merge_serve(self, gitlabs, local_branchs, merge_branch):
        """
            执行merge 请求，获得merge的url
        """

        gitlab = str(gitlabs.first()).split(sdk_git_path)[-1]
        local_branch = local_branchs.first()
        logger.info("gitlab:%s, local_branch:%s, merge_branch:%s", gitlab, local_branch, merge_branch)
        response = git_client.merge_serve(gitlab, local_branch, merge_branch)
        logger.info("调用git, merge状态信息: %s", response)
        if response.mr_url and response.mr_iid:
            logger.info("merge 合并的 url: %s,  response.mr_iid:%s", response.mr_url,  response.mr_iid)
        return response.mr_url,  response.mr_iid



    def post(self, request):
        try:
            # 获取参数
            params = request.data.get('params', {})
            logger.info("receive info:%s", params)
            workspace_path = params.get("workspace_path")
            merge_branch = params.get("merge_branch")
            logger.info("workspace_path:%s, merge_branch:%s", workspace_path, merge_branch)
            # 根据工作空间查询分支信息表中的分支和gitlab地址
            project = CodeBranchInfo.objects.filter(
                branch_space=workspace_path
            )
            logger.info("在分支信息表中查询到的数据:%s", project)
            gitlabs = project.values_list('project_gitlab', flat=True).distinct()
            local_branchs = project.values_list('project_branch', flat=True).distinct()
            create_persons = project.values_list('create_person', flat=True).distinct()
            logger.info("gitlabs:%s, local_branchs:%s, create_persons:%s", gitlabs, local_branchs, create_persons)

            user = request.user
            logger.info("user:%s, %s", user, type(user))
            # user = "T050959"
            # 获取当前人，以及当前人员上级得邮箱
            persons_email, leader_email = get_user_email(user)
            logger.info("persons_email: %s, leader_email: %s", persons_email, leader_email)

            # 调用 merge 请求
            mr_url, mr_iid = self.send_merge_serve(gitlabs, local_branchs, merge_branch)

            # 将mr_iid存入数据库
            affected_rows = CodeBranchInfo.objects.filter(branch_space=workspace_path).update(mr_iid=mr_iid)

            if affected_rows == 1:
                logger.info("更新成功")
            elif affected_rows > 1:
                logger.info(f"警告：更新了 {affected_rows} 条记录（可能匹配多个）")
            else:
                logger.info("未找到符合条件的数据")
                raise ValueError("未找到符合条件的数据")

            project = CodeBranchInfo.objects.filter(
                branch_space=workspace_path
            )
            logger.info("插入mr_iid后, 在分支信息表中查询到的数据:%s", project)
            # 给当前分支创建人发送飞书卡片信息
            leader_result = self.send_leader_fs_info(leader_email, gitlabs, local_branchs, merge_branch, mr_url)
            logger.info("leader_result: %s", leader_result)
            # 最终统一返回逻辑
            if leader_result:
                return Response({"merge_status": 1, "message": "给分支创建人的上级发送飞书消息提示成功"})
            else:
                return Response({"merge_status": 0, "message": "给分支创建人的上级发送飞书消息提示失败"})

        except Exception as e:
            logger.info("meger流程失败: %s", str(e))
            return Response({"merge_status": 0, "message": "给分支创建人的上级发送飞书消息提示失败"})





