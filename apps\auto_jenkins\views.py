from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.http import Http404
from django.core.cache import cache
import logging
import os
import gitlab
from datetime import datetime
from .jenkins_service import JenkinsService
from .generatior import JenkinsfileGenerator
from .step_types import step_type_registry
from .models import PipelineProject, PipelineBuild

logger = logging.getLogger("auto_jenkins")

GITLAB_URL = "http://*********"   
GITLAB_TOKEN = "**************************"

# ========== Git项目相关API ==========

class GitProjectsView(APIView):
    """获取Git项目列表"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取MCU和python组的所有项目"""
        engineering_groups = ['MCU', 'python-team']  # 限定这两个组
        all_projects = []
        
        for group_name in engineering_groups:
            cache_key = f"gitlab:projects:{group_name}"
            cached_data = cache.get(cache_key)
            
            if cached_data:
                logger.info(f"Cache hit for {group_name}")
                for project in cached_data:
                    project['engineering_group'] = group_name
                all_projects.extend(cached_data)
            else:
                # 从GitLab获取项目
                try:
                    gl = gitlab.Gitlab(url=GITLAB_URL, private_token=GITLAB_TOKEN)
                    group = gl.groups.get(group_name)
                    projects = self._get_all_projects_recursive(gl, group.id)
                    
                    # 添加工程组信息
                    for project in projects:
                        project['engineering_group'] = group_name
                    
                    all_projects.extend(projects)
                    
                    # 缓存7天
                    cache.set(cache_key, projects, timeout=7 * 24 * 3600)
                    logger.info(f"Retrieved {len(projects)} projects for {group_name}")
                    
                except Exception as e:
                    logger.error(f"Error retrieving projects for {group_name}: {str(e)}")
        
        return Response({
            "success": True,
            "data": {
                "projects": all_projects,
                "total_count": len(all_projects)
            }
        })
    
    def _get_all_projects_recursive(self, gl, group_id):
        """递归获取组下所有项目"""
        all_projects = []
        visited_groups = set()

        def fetch_group_projects(group_id):
            if group_id in visited_groups:
                return
            visited_groups.add(group_id)

            group = gl.groups.get(group_id)
            page = 1
            while True:
                projects = group.projects.list(page=page, per_page=100)
                if not projects:
                    break
                for group_project in projects:
                    project = gl.projects.get(group_project.id)
                    branches = project.branches.list()
                    branch_names = [branch.name for branch in branches]
                    all_projects.append({
                        "project": project.name,
                        "path": group_project.path_with_namespace,
                        "git_url": project.ssh_url_to_repo,
                        "branches": branch_names
                    })
                page += 1
            
            # 处理子组
            subgroups = group.subgroups.list()
            for subgroup in subgroups:
                fetch_group_projects(subgroup.id)
        
        fetch_group_projects(group_id)
        return all_projects



class PipelineProjectsView(APIView):
    """Pipeline项目管理"""
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取所有Pipeline项目"""
        projects = PipelineProject.objects.all().order_by('-updated_at')
        jenkins_service = JenkinsService()
        
        results = []
        for project in projects:
            # 检查Jenkins Job状态
            jenkins_exists = jenkins_service.check_job_exists(project.jenkins_job_name)
            jenkins_info = None
            if jenkins_exists:
                jenkins_info = jenkins_service.get_job_info(project.jenkins_job_name)
            
            results.append({
                "id": project.id,
                "project_name": project.project_name,
                "git_url": project.git_url,
                "engineering_group": project.engineering_group,
                "engineering_path": project.engineering_path,
                "agent": project.agent,
                "jenkins_job_name": project.jenkins_job_name,
                "jenkins_exists": jenkins_exists,
                "jenkins_info": jenkins_info,
                "created_at": project.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                "updated_at": project.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                "creator": project.creator
            })
        
        return Response({
            "success": True,
            "data": {
                "projects": results,
                "total_count": len(results)
            }
        })
    
    def post(self, request):
        """创建Pipeline项目"""
        data = request.data
        project_name = data.get('project_name')
        git_url = data.get('git_url')
        engineering_group = data.get('engineering_group')
        engineering_path = data.get('engineering_path')
        agent = data.get('agent', 'slave_win10')
        branch = data.get('branch', 'master')
        pipeline_config = data.get('pipeline_config')
        
        # 验证必需参数
        if not all([project_name, git_url, branch, pipeline_config]):
            return Response({
                "success": False,
                "message": "缺少必需参数"
            }, status=status.HTTP_400_BAD_REQUEST)
        # raise Exception("手动中断:调试用")
        try:
            # 检查项目是否已存在
            if PipelineProject.objects.filter(project_name=project_name).exists():
                return Response({
                    "success": False,
                    "message": "项目名称已存在"
                }, status=status.HTTP_400_BAD_REQUEST)
                 
            # 生成Jenkinsfile
            generator = JenkinsfileGenerator()
            generation_result = generator.generate(pipeline_config)
            if not generation_result['success']:
                return Response({
                    "success": False,
                    "message": "生成Jenkinsfile失败",
                    "errors": generation_result['errors']
                }, status=status.HTTP_400_BAD_REQUEST)
            jenkinsfile_content = generation_result['jenkinsfile']
            jenkins_job_name = f"auto-{project_name}"
            
            # 创建Jenkins Job
            jenkins_service = JenkinsService()
            
            
            jenkins_success = jenkins_service.create_or_update_pipeline_job(
                jenkins_job_name, 
                jenkinsfile_content,
                git_url,
                pipeline_config,
                branch,
                engineering_path
            )
            print(jenkins_success)
            
            if not jenkins_success:
                return Response({
                    "success": False,
                    "message": "创建Jenkins Job失败"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            


            # 保存到数据库
            project = PipelineProject.objects.create(
                project_name=project_name,
                git_url=git_url,
                engineering_group=engineering_group,
                engineering_path=engineering_path,
                agent=agent,
                pipeline_config=pipeline_config,
                jenkinsfile_content=jenkinsfile_content,
                jenkins_job_name=jenkins_job_name,
                creator=request.user.username if request.user.is_authenticated else 'system'
            )
            
            return Response({
                "success": True,
                "message": "Pipeline项目创建成功",
                "data": {
                    "id": project.id,
                    "project_name": project.project_name,
                    "jenkins_job_name": jenkins_job_name
                }
            })
            
        except Exception as e:
            logger.error(f"创建Pipeline项目失败: {str(e)}")
            return Response({
                "success": False,
                "message": f"创建失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class PipelineProjectDetailView(APIView):
    """Pipeline项目详情"""
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, project_name):
        """获取项目详情"""
        try:
            project = PipelineProject.objects.get(project_name=project_name)
            jenkins_service = JenkinsService()
            
            # 获取Jenkins状态
            jenkins_exists = jenkins_service.check_job_exists(project.jenkins_job_name)
            jenkins_info = None
            if jenkins_exists:
                jenkins_info = jenkins_service.get_job_info(project.jenkins_job_name)
            
            return Response({
                "success": True,
                "data": {
                    "id": project.id,
                    "project_name": project.project_name,
                    "git_url": project.git_url,
                    "engineering_group": project.engineering_group,
                    "engineering_path": project.engineering_path,
                    "agent": project.agent,
                    "pipeline_config": project.pipeline_config,
                    "jenkinsfile_content": project.jenkinsfile_content,
                    "jenkins_job_name": project.jenkins_job_name,
                    "jenkins_exists": jenkins_exists,
                    "jenkins_info": jenkins_info,
                    "created_at": project.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    "updated_at": project.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                    "creator": project.creator
                }
            })
            
        except PipelineProject.DoesNotExist:
            return Response({
                "success": False,
                "message": "项目不存在"
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"获取项目详情失败: {str(e)}")
            return Response({
                "success": False,
                "message": f"获取失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def put(self, request, project_name):
        """更新项目配置"""
        try:
            project = PipelineProject.objects.get(project_name=project_name)
            data = request.data
            
            # 更新字段
            if 'agent' in data:
                project.agent = data['agent']
            if 'pipeline_config' in data:
                project.pipeline_config = data['pipeline_config']
                
                # 重新生成Jenkinsfile
                generator = JenkinsfileGenerator()
                generation_result = generator.generate(data['pipeline_config'])
                if not generation_result['success']:
                    return Response({
                        "success": False,
                        "message": "生成Jenkinsfile失败",
                        "errors": generation_result['errors']
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                project.jenkinsfile_content = generation_result['jenkinsfile']
                
                # 更新Jenkins Job
                jenkins_service = JenkinsService()
                jenkins_success = jenkins_service.create_or_update_pipeline_job(
                    project.jenkins_job_name,
                    project.jenkinsfile_content,
                    project.git_url,
                    project.pipeline_config,
                    'master',  # TODO: 根据业务需要替换为 project.branch 字段
                    project.engineering_path
                )
                
                if not jenkins_success:
                    return Response({
                        "success": False,
                        "message": "更新Jenkins Job失败"
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            project.save()
            
            return Response({
                "success": True,
                "message": "项目更新成功"
            })
            
        except PipelineProject.DoesNotExist:
            return Response({
                "success": False,
                "message": "项目不存在"
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"更新项目失败: {str(e)}")
            return Response({
                "success": False,
                "message": f"更新失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def delete(self, request, project_name):
        """删除项目"""
        try:
            project = PipelineProject.objects.get(project_name=project_name)
            jenkins_job_name = project.jenkins_job_name
            
            # 删除数据库记录
            project.delete()
            
            # TODO: 可选择是否删除Jenkins Job
            # jenkins_service = JenkinsService()
            # jenkins_service.delete_job(jenkins_job_name)
            
            return Response({
                "success": True,
                "message": "项目删除成功"
            })
            
        except PipelineProject.DoesNotExist:
            return Response({
                "success": False,
                "message": "项目不存在"
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"删除项目失败: {str(e)}")
            return Response({
                "success": False,
                "message": f"删除失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# ========== Pipeline构建执行API ==========

class PipelineBuildView(APIView):
    """Pipeline构建执行"""
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]
    
    def post(self, request, project_name):
        """触发Pipeline构建"""
        try:
            project = PipelineProject.objects.get(project_name=project_name)
            jenkins_service = JenkinsService()
            
            # 获取构建参数
            build_params = request.data.get('parameters', {})
            
            # 检查Jenkins Job是否存在
            if not jenkins_service.check_job_exists(project.jenkins_job_name):
                # 重新创建Jenkins Job
                jenkins_success = jenkins_service.create_or_update_pipeline_job(
                    project.jenkins_job_name,
                    project.jenkinsfile_content,
                    project.git_url,
                    project.pipeline_config,
                    'master',  # TODO: 根据业务需要替换为 project.branch 字段
                    project.engineering_path
                )
                if not jenkins_success:
                    return Response({
                        "success": False,
                        "message": "Jenkins Job不存在且创建失败"
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # 触发构建
            try:
                task_id, queue_id = jenkins_service.start_jenkins_job(
                    project.jenkins_job_name,
                    build_params
                )
            except Exception as e:
                return Response({
                    "success": False,
                    "message": f"触发构建失败: {str(e)}"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            return Response({
                "success": True,
                "message": "构建已触发",
                "data": {
                    "task_id": task_id,
                    "project_name": project_name,
                    "jenkins_job_name": project.jenkins_job_name,
                    "queue_id": queue_id
                }
            })
            
        except PipelineProject.DoesNotExist:
            return Response({
                "success": False,
                "message": "项目不存在"
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"触发构建失败: {str(e)}")
            return Response({
                "success": False,
                "message": f"触发失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# ========== 保留现有的API ==========

class JenkinsTaskStatusView(APIView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.jenkins_service = JenkinsService()

    def get(self, request, task_id):
        """查询Jenkins任务状态"""
        logger.info(f"GET /auto_jenkins/tasks/{task_id}/status called")
        
        try:
            # 使用增强的任务状态查询
            result = self.jenkins_service.get_enhanced_task_status(task_id)
            
            if not result["success"]:
                return Response({
                    "success": False,
                    "message": result.get("error", "任务不存在或已过期")
                }, status=status.HTTP_404_NOT_FOUND)
            
            task_data = result["data"]
            data_source = result.get("source", "unknown")
            
            # 构造响应数据，不包含详细日志
            response_data = {
                "task_id": task_data["task_id"],
                "status": task_data["status"],
                "progress": task_data["progress"],
                "start_time": task_data["start_time"],
                "end_time": task_data["end_time"],
                "jenkins_build_number": task_data["jenkins_build_number"],
                "build_cmd": task_data.get("build_cmd"),
                "upload_cmd": task_data.get("upload_cmd"),
                "latest_logs": task_data["logs"][-5:] if task_data.get("logs") else [],  # 只返回最新5条日志
                "total_logs_count": len(task_data.get("logs", [])),
                "data_source": data_source  # 标识数据来源
            }
            
            if task_data.get("error_message"):
                response_data["error_message"] = task_data["error_message"]
            
            logger.info(f"任务状态查询成功: {task_id}, 来源: {data_source}")
            return Response({
                "success": True,
                "data": response_data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            error_msg = f"查询任务状态失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return Response({
                "success": False,
                "message": error_msg
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class JenkinsTaskLogsView(APIView):
    """获取任务日志"""
    def get(self, request, task_id):
        jenkins_service = JenkinsService()
        task_data = jenkins_service.get_task_data(task_id)
        
        if not task_data:
            return Response({
                "success": False,
                "message": "任务不存在或已过期"
            }, status=status.HTTP_404_NOT_FOUND)
        
        return Response({
            "success": True,
            "data": {
                "task_id": task_id,
                "logs": task_data.get("logs", [])
            }
        })

class JenkinsBuildStatusView(APIView):
    """直接查询Jenkins构建状态"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.jenkins_service = JenkinsService()

    def get(self, request, job_name, build_number=None):
        """查询指定Jenkins任务的构建状态"""
        logger.info(f"GET /auto_jenkins/jenkins-builds/{job_name}/{build_number or 'latest'}/status called")
        
        try:
            if build_number:
                # 查询指定构建号的状态
                result = self.jenkins_service.get_build_status_from_jenkins(job_name, int(build_number))
            else:
                # 查询最新构建状态
                result = self.jenkins_service.get_latest_build_status(job_name)
            
            if not result["success"]:
                return Response({
                    "success": False,
                    "message": result.get("error", "查询构建状态失败")
                }, status=status.HTTP_404_NOT_FOUND)
            
            logger.info(f"Jenkins构建状态查询成功: {job_name}#{build_number or 'latest'}")
            return Response({
                "success": True,
                "data": result["data"]
            }, status=status.HTTP_200_OK)
            
        except ValueError:
            return Response({
                "success": False,
                "message": "构建号必须是数字"
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            error_msg = f"查询Jenkins构建状态失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return Response({
                "success": False,
                "message": error_msg
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

