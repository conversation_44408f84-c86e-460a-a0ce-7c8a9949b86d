from django.urls import path
from .views import (
    GitProjectsView,
    PipelineProjectsView, 
    PipelineProjectDetailView,
    PipelineBuildView,
    JenkinsTaskStatusView,
    JenkinsTaskLogsView,
    JenkinsBuildStatusView
)

urlpatterns = [
    # Git项目相关
    path("git-projects/", GitProjectsView.as_view(), name="git_projects"),
    
    # Pipeline项目管理
    path("projects/", PipelineProjectsView.as_view(), name="pipeline_projects"),
    path("projects/<str:project_name>/", PipelineProjectDetailView.as_view(), name="pipeline_project_detail"),
    
    # Pipeline构建执行
    path("projects/<str:project_name>/build/", PipelineBuildView.as_view(), name="pipeline_build"),
    
    # 任务状态查询
    path("tasks/<str:task_id>/status/", JenkinsTaskStatusView.as_view(), name="task_status"),
    path("tasks/<str:task_id>/logs/", JenkinsTaskLogsView.as_view(), name="task_logs"),
    
    # Jenkins构建状态直接查询
    path("jenkins-builds/<str:job_name>/latest/status/", JenkinsBuildStatusView.as_view(), name="jenkins_build_latest_status"),
    path("jenkins-builds/<str:job_name>/<int:build_number>/status/", JenkinsBuildStatusView.as_view(), name="jenkins_build_status"),
    
]
