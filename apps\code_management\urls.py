# src/code_management/urls.py
from django.urls import path

from .project_views import (
    SdkInfoView,
    ConfigSubmitView,
    ProjectInfoView,
    DeleteProjectView,
    ProjectSubmitView,
    GetBranchesView,
    InitGrpcView
)

from .branch_views import (
    ProjectSpaceView,
    ProjectBranchView,
    BranchSubmitView
)

from .config_views import (
    ConfigParamsView,
    ConfigInfoView,
    ConfigCommitView,
    ConfigPushView,
    MergeProjectView
)

from .chip_views import (
    ChipViews,
    ChipConfigViews,
    ChipModulViews,
    chipChangeViews
)

urlpatterns = [
    path('/get_branches', GetBranchesView.as_view()),
    path('/init_project', InitGrpcView.as_view()),
    path('/project_submit', ProjectSubmitView.as_view()),
    path('/config_submit', ConfigSubmitView.as_view()),
    path('/configuration_info', ConfigInfoView.as_view()),
    path('/config_params', ConfigParamsView.as_view()),
    path('/config_commit', ConfigCommitView.as_view()),
    path('/config_push', ConfigPushView.as_view()),
    path('/project_info', ProjectInfoView.as_view()),
    path('/branch_options', ProjectBranchView.as_view()),
    path('/space_options', ProjectSpaceView.as_view()),
    path('/sdk_info', SdkInfoView.as_view()),
    path('/branch_submit', BranchSubmitView.as_view()),
    path('/delete_project', DeleteProjectView.as_view()),
    path('/merge_project', MergeProjectView.as_view()),
    path('/chip_info', ChipViews.as_view()),
    path('/chip_config', ChipConfigViews.as_view()),
    path('/chip_modul', ChipModulViews.as_view()),
    path('/chip_change', chipChangeViews.as_view())

]
