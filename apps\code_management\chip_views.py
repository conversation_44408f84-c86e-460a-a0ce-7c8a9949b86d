import logging
import traceback
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from django.db import transaction
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.cache import cache
from django.core.serializers import serialize
import json
import os
import shutil
import time
from pathlib import Path
from django.db import models
from django.contrib.postgres.fields import ArrayField

logger = logging.getLogger("code_management")
from apps.code_management.grpc.host_machine_grpc import host_machine_client
from apps.code_management.grpc.git_grpc import git_client
from .models import (
    CodeBranchInfo,
    Chip,
    ChipColor,
    ChipMap,
    ChipGPIO,
    ChipADC,
    ChipPWM,
    ChipIIC,
    ChipSPI,
    ChipEXTI,
    ChipUART,
ChipGpioSerializer,
ChipSPISerializer,
ChipUARTSerializer,
ChipIICSerializer,
ChipADCSerializer,
ChipPWMSerializer,
ChipEXTISerializer


)
from users.models import UserFSInfo
from utils.fs_service import FSService
from utils.fs_app import fs_app


local_path = "/home/<USER>/hwcp"
config_path = "/home/<USER>/hwcp/gRPC_Server/HwcpTest"
config_url = "http:/,fanh*********/python-team/HwcpTest.git"
config_branch = "main"
sdk_git_path = "http://*********/"


def queryset_to_dict(queryset_or_instance):
    """
    通用函数：将 QuerySet 或模型实例转换为字典格式
    自动处理不同字段类型，包括 ArrayField、JSONField 等
    """
    def model_instance_to_dict(instance):
        """将单个模型实例转换为字典"""
        data = {}

        # 获取模型的所有字段
        for field in instance._meta.fields:
            field_name = field.name
            field_value = getattr(instance, field_name)

            # 处理不同类型的字段
            if field_value is None:
                data[field_name] = None
            elif hasattr(field, 'base_field'):  # ArrayField
                # PostgreSQL ArrayField 转换为列表
                data[field_name] = list(field_value) if field_value else []
            elif isinstance(field, models.JSONField):
                # JSONField 保持原样（已经是 dict 或 list）
                data[field_name] = field_value
            elif isinstance(field, (models.DateTimeField, models.DateField, models.TimeField)):
                # 日期时间字段转换为字符串
                data[field_name] = field_value.isoformat() if field_value else None
            elif isinstance(field, models.BooleanField):
                # 布尔字段
                data[field_name] = bool(field_value)
            elif isinstance(field, (models.IntegerField, models.BigIntegerField, models.SmallIntegerField)):
                # 整数字段
                data[field_name] = int(field_value) if field_value is not None else None
            elif isinstance(field, (models.FloatField, models.DecimalField)):
                # 浮点数字段
                data[field_name] = float(field_value) if field_value is not None else None
            else:
                # 其他字段类型（CharField, TextField 等）转换为字符串
                data[field_name] = str(field_value) if field_value is not None else None

        return data

    # 判断输入类型并处理
    if hasattr(queryset_or_instance, '__iter__') and not isinstance(queryset_or_instance, (str, dict)):
        # QuerySet 或列表
        return [model_instance_to_dict(instance) for instance in queryset_or_instance]
    else:
        # 单个模型实例
        return model_instance_to_dict(queryset_or_instance)


def get_label(field_name):
    """
    获取字段名对应的中文 label（假设 ChipMap 表中有 name 和 label 两个字段）
    """
    try:
        logger.info("field_name: %s", field_name)
        label_obj = ChipMap.objects.using("hwcp").filter(name=field_name)
        data = dict()
        for item in label_obj:
            data = {
                "name": item.name,
                "label": item.label
            }
        logger.info("data: %s", data)
        logger.info("data.label: %s", data["label"])
        return data["label"]  # 假设字段叫 label
    except ChipMap.DoesNotExist:
        return field_name  # 找不到就返回原字段名



def get_name(u_name):
    """
    获取字段名对应的中文 label（假设 ChipMap 表中有 name 和 label 两个字段）
    """
    try:
        logger.info("u_name: %s", u_name)
        name_obj = ChipMap.objects.using("hwcp").filter(label=u_name)
        data = dict()
        for item in name_obj:
            data = {
                "name": item.name,
                "label": item.label
            }
        logger.info("data: %s", data)
        logger.info("data.name: %s", data["name"])
        return data["name"]  # 假设字段叫 label
    except ChipMap.DoesNotExist:
        return u_name  # 找不到就返回原字段名

class ChipViews(APIView):

    def get_model_data(self, model_class, database="hwcp", filters=None, **kwargs):
        """
        通用方法：获取任意模型的数据并转换为字典格式

        Args:
            model_class: Django 模型类
            database: 数据库别名，默认为 "hwcp"
            filters: 过滤条件字典，如 {"chip": "KF32A158"}
            **kwargs: 其他查询参数

        Returns:
            list: 转换后的字典列表
        """
        try:
            # 构建查询
            queryset = model_class.objects.using(database)

            # 应用过滤条件
            if filters:
                queryset = queryset.filter(**filters)

            # 应用其他查询参数
            if kwargs:
                queryset = queryset.filter(**kwargs)

            # 获取所有数据
            data = queryset.all()

            # 转换为字典格式
            return queryset_to_dict(data)

        except Exception as e:
            logger.error(f"获取 {model_class.__name__} 数据失败: {e}")
            return []


    def get_chip(self, project_code, project_gitlab, project_branch):
        # 查找芯片名称
        try:
            project = CodeBranchInfo.objects.only("chip").get(
                project_code=project_code,
                project_gitlab=project_gitlab,
                project_branch=project_branch
            )
        except CodeBranchInfo.DoesNotExist:
            return Response({"message": "未找到项目"})
        except CodeBranchInfo.MultipleObjectsReturned:
            return Response({"message": "存在多个匹配项目，请检查数据"})
        # 如果查到，返回 chip 字段
        chip_value = project.chip
        return chip_value


    def get_module_color(self):
        # 获取模块颜色 - 使用通用方法
        return self.get_model_data(ChipColor)


    def get_chip_table(self, chip):
        # 查询所有module字段，返回QuerySet
        modules_qs = ChipColor.objects.using("hwcp").values_list('module', flat=True)
        # 如果需要转成列表
        io = list(modules_qs)

        # 获取引脚配置表数据 - 使用通用方法
        table_info = self.get_model_data(Chip, filters={"chip": chip})

        return io, table_info


    def get(self, request):
        try:
            params = request.query_params
            logger.info("receive info: %s", params)
            project_code = params.get("project_code")
            project_gitlab = params.get("project_gitlab")
            project_branch = params.get("project_branch")

            # 参数验证
            if not all([project_code, project_gitlab, project_branch]):
                return Response({
                    "status": 0,
                    "message": "缺少必要参数，请提供 project_code, project_gitlab, project_branch"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 获取芯片
            chip = self.get_chip(project_code, project_gitlab, project_branch)

            # 如果 get_chip 返回了 Response 对象，说明出错了
            if isinstance(chip, Response):
                return chip

            # 获取引脚颜色
            color = self.get_module_color()

            # 获取引脚配置表数据
            io, table_info = self.get_chip_table(chip)
            logger.info("chip: %s, color:%s, io:%s, table:%s", chip, color, io, table_info)
            return Response({
                "status": 1,
                "message": "数据获取成功",
                "data": {
                            "chip": chip,
                            "color": color,
                            "io": io,
                            "table": table_info
                        }
                })

        except Exception as e:
            logger.error("获取芯片配置数据失败: %s", str(e))
            logger.error(traceback.format_exc())
            return Response({
                "status": 0,
                "message": f"获取芯片配置数据失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_any_chip_data(self, request):
        """
        演示方法：展示如何使用通用函数获取任意芯片相关数据
        支持动态查询不同的芯片模型数据
        """
        try:
            # 获取查询参数
            model_name = request.query_params.get("model", "chip")  # 默认查询 chip 表
            chip_name = request.query_params.get("chip")

            # 模型映射
            model_mapping = {
                "chip": Chip,
                "chip_color": ChipColor,
                "chip_gpio": ChipGPIO,
                "chip_adc": ChipADC,
                "chip_pwm": ChipPWM,
                "chip_iic": ChipIIC,
                "chip_spi": ChipSPI,
                "chip_uart": ChipUART,
                "chip_exti": ChipEXTI,
                "chip_map": ChipMap,
            }

            # 获取对应的模型类
            model_class = model_mapping.get(model_name.lower())
            if not model_class:
                return Response({
                    "status": 0,
                    "message": f"不支持的模型: {model_name}，支持的模型: {list(model_mapping.keys())}"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 构建过滤条件
            filters = {}
            if chip_name and hasattr(model_class, 'chip'):
                filters['chip'] = chip_name

            # 使用通用方法获取数据
            data = self.get_model_data(model_class, filters=filters)

            return Response({
                "status": 1,
                "message": f"成功获取 {model_name} 数据",
                "data": {
                    "model": model_name,
                    "count": len(data),
                    "items": data
                }
            })

        except Exception as e:
            logger.error(f"获取 {model_name} 数据失败: {e}")
            logger.error(traceback.format_exc())
            return Response({
                "status": 0,
                "message": f"获取数据失败: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



class ChipConfigViews(APIView):





    def get_chipinfo(self, pin_id, chip):
        """
        获取 Chip 配置并格式化为包含 label 的字段列表
        """
        info = Chip.objects.using("hwcp").filter(chip=chip, pin_id=pin_id)

        formatted_data = []

        for item in info:
            fields = ["pin_id", "pin_name", "module"]
            for field in fields:
                if field == "id":
                    continue
                value = getattr(item, field, None)
                logger.info("value: %s", value)
                label = get_label(field)
                formatted_data.append({
                    "key": field,
                    "display": label,
                    "value": value,
                })

        return formatted_data

    def get(self, request):
        try:
            params = request.query_params
            logger.info("receive info: %s", params)
            pin_id = params.get("pinId")
            chip = params.get("chip")

            # 获取引脚信息
            pin_info = self.get_chipinfo(pin_id, chip)

            logger.info("pin_info: %s", pin_info)
            return Response({
                "status": 1,
                "message": "数据获取成功",
                "data": {
                    "pin_info": pin_info
                }
            })

        except Exception as e:
            logger.info("获取引脚数据失败: %s", str(e))
            return Response({"status": 0, "message": "Exception:" + str(e)})



class ChipModulViews(APIView):




    def get_module(self, module):
        model_name = None
        Serializer_name = None
        if module == "GPIO":
            logger.info("这是通用输入输出 GPIO")
            model_name = ChipGPIO
            Serializer_name = ChipGpioSerializer
        elif module == "SPI":
            logger.info("这是串行外设接口 SPI")
            model_name = ChipSPI
            Serializer_name = ChipSPISerializer
        elif module == "UART":
            logger.info("这是串口 UART")
            model_name = ChipUART
            Serializer_name = ChipUARTSerializer
        elif module == "IIC":
            logger.info("这是 I²C 接口")
            model_name = ChipIIC
            Serializer_name = ChipIICSerializer
        elif module == "ADC":
            logger.info("这是模拟转数字 ADC")
            model_name = ChipADC
            Serializer_name = ChipADCSerializer
        elif module == "PWM":
            logger.info("这是 PWM 波形输出")
            model_name = ChipPWM
            Serializer_name = ChipPWMSerializer
        elif module == "EXIT":
            logger.info("这是外部中断 EXIT")
            model_name = ChipEXTI
            Serializer_name = ChipEXTISerializer
        elif module == "OTHER":
            logger.info("这是其他功能模块")
        else:
            logger.info("不是设定中的模块")

        if model_name:
            logger.info("model_name:%s", model_name)
            data = model_name.objects.all()
            # 使用序列化器
            serializer = Serializer_name(data, many=True)

            # # 取序列化后的数据
            info = serializer.data

            # 转换为表单字段配置
            form_config = []
            if info:  # 确保不为空
                logger.info("info: %s", info)

                # 获取第一个配置对象（OrderedDict）
                item = info[0]
                for key, value in item.items():
                    if key == "id":
                        continue

                    field = {
                        "key": key,
                        "label": get_label(key),
                    }

                    if isinstance(value, list):
                        field["type"] = "select"
                        field["options"] = [{"label": v, "value": v} for v in value]

                    elif isinstance(value, dict) and "min" in value and "max" in value:
                        field["type"] = "number"
                        field["min"] = int(value["min"])
                        field["max"] = int(value["max"])

                    else:
                        field["type"] = "text"
                        field["placeholder"] = f"请输入 {get_label(key)}"

                    form_config.append(field)


        else:
            form_config = None

        return form_config






    def get(self, request):

        try:
            params = request.query_params
            logger.info("receive info: %s", params)
            module = params.get("module")

            # 获取引脚信息
            module_info = self.get_module(module)

            logger.info("module_info: %s", module_info)
            return Response({
                "status": 1,
                "message": "数据获取成功",
                "data": {
                    "module": module_info
                }
            })

        except Exception as e:
            logger.info("获取引脚模块数据失败: %s", str(e))
            return Response({"status": 0, "message": "Exception:" + str(e)})


#
class chipChangeViews(APIView):


    def modify_chip_info(self, sdk_path, workspace_path, branch_status, config_module, config_item, config_value, chip_name, pin_id, name):

        logger.info("receive info: sdk_path = %s", sdk_path)
        logger.info("receive info: workspace_path = %s", workspace_path)
        logger.info("receive info: branch_status = %s", branch_status)
        logger.info("receive info: config_module = %s", config_module)
        logger.info("receive info: config_item = %s", config_item)
        logger.info("receive info: config_value = %s", config_value)
        logger.info("receive info: chip_name = %s", chip_name)
        logger.info("receive info: pin_id = %s", pin_id)
        logger.info("receive info: name = %s", name)

        response = host_machine_client.modify_chip_parms(sdk_path, config_module, config_item, config_value, chip_name, pin_id, name, workspace_path, branch_status)

        logger.info(" 修改mcu芯片信息 response = %s", response)

        if response.config_status == 1:
            return True
        else:
            return False


    def get(self, request):
        try:
            params = request.query_params
            logger.info("receive info: %s", params)

            workspace_path = params.get("workspace_path")
            branch_status = params.get('branch_status')
            chip_name = params.get("chip_name")
            pin_id = params.get("pin_number")
            name = params.get("name")
            config_module = params.get("function_type")
            label = params.get("change_label")
            config_value = params.get("change_value")

            sdk_path = os.path.dirname(workspace_path) + "/" + "hiwaysdk_2.0" + '/' + "Application"

            if not name or not name.strip():
                logger.info("name 字段为空或全是空格")
                raise ValueError("name 字段为空，无法进行配置修改")

            # 通过页面标签获取name
            item = get_name(label)

            if item == "type":
                    config_item = "pininfo"
            elif item != "name" or item != "des":
                config_item = "config/" + item
            else:
                config_item = item
            # 请求grpc修改配置
            res = self.modify_chip_info(sdk_path, workspace_path, branch_status, config_module, config_item, config_value, chip_name, pin_id, name)
            if res:
                return Response({
                    "status": 1,
                    "message": "数据修改成功",
                })
            else:
                raise ValueError("请检查UpdateGPIOConfigItem服务")
        except Exception as e:
            logger.info("修改引脚数据失败: %s", str(e))
            return Response({"status": 0, "message": "Exception:" + str(e)})
