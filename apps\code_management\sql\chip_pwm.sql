CREATE TABLE public.chip_pwm (
    id SERIAL PRIMARY KEY,
    type VARCHAR(20)[] DEFAULT '{}',                 -- 类型
    work_model VARCHAR(20)[] DEFAULT '{}',           -- 工作模式
    alt_value VARCHAR(50),                           -- 复用值
    channel VARCHAR(50),                             -- 通道号
    clock_division VARCHAR(5)[] DEFAULT '{}',        -- 时钟分频
    output_frequency VARCHAR(50),                    -- 输出频率
    duty_cycle JSONB DEFAULT '{}'::jsonb,            -- 占空比 [%]
    enable_config VARCHAR(10)[] DEFAULT '{}'         -- 是否启用配置
);


ALTER TABLE public.chip_pwm
ADD COLUMN name VARCHAR(100),  -- 名称
ADD COLUMN des TEXT;           -- 描述