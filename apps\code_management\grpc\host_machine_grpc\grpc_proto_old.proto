syntax = "proto3";

message WorkSpaceInfo{
    string sdk_path = 1;//sdk所在路径
    string project_path = 2;//(工作空间路径)
    string branch_status = 3;//[0表示现有分支，1表示新建分支]
}

service HWCPService {
    rpc GetProjectTreeData(GetProjectTreeDataParams) returns (ProjectTreeDataResponse);
    rpc ConfigChipFunctions(ConfigChipFunctionsParams) returns (ConfigChipFunctionsResponse);
    rpc GetSubFunctionItems(SubFunction) returns (ProjectSubFunctionItemsResponse);
    rpc UpdateConfigItem(ConfigParams) returns (UpdateConfigItemResponse);
    rpc GetMCUChipInfo(GetMCUChipInfoParams) returns (GetMCUChipInfoParamsResponse);
    rpc GetGPIOInfo(GetGPIOInfoParams) returns (GetGPIOInfoParamsResponse);
    rpc	UpdateGPIOConfigItem(UpdateGPIOConfigItemParams) returns (UpdateGPIOConfigItemParamsResponse);
}

message GetProjectTreeDataParams{
    string sdk_path= 1;//sdk所在路径
}

message ProjectTreeDataResponse{
    repeated string module_list = 1;    //["Brightness","DataFlash","Temperature","Touch"]
    repeated string chip_list = 2;      //["RH850F1KM","FC4150","KF32A158"]
}

message ConfigChipFunctionsParams{
    WorkSpaceInfo work_space_info = 1;
    string chip_type = 2;               //RH850 F1kM
    string functions = 3;               //Brightness&DataFlash&Temperature&Touch
}

message ConfigChipFunctionsResponse{
    int32 config_status = 1;            //【0标识配置失败，1标识配置成功】
    string project_tree_data = 2;       //模块树信息
}

message SubFunction{
    WorkSpaceInfo work_space_info = 1;
    string project_tree_item = 2;       // Brightness/Config_Brightness
}

message ProjectSubFunctionItemsResponse{
    string sub_function_items = 1;
}

message ConfigParams{
    WorkSpaceInfo work_space_info = 1;
    string config_path = 2;             //Brightness/Config_Brightness/背光IC选择
    string config_value = 3;            //100
}

message UpdateConfigItemResponse{
    int32 config_status = 1;            //【0标识配置失败，1标识配置成功】
}

message GetMCUChipInfoParams{
    WorkSpaceInfo work_space_info = 1;
    string chip_name = 2;               //MCU型号 示例：RH850F1KM FC4150 KF32A158
    string hal_path = 3;                //HalSdk对应型号目录 示例：HalSdk/RH850F1KM
}

message GetMCUChipInfoParamsResponse{
    int32 config_status = 1;            //【0标识配置失败，1标识配置成功】
    string MCU_chip_info = 2;
}

message GetGPIOInfoParams{
	  WorkSpaceInfo work_space_info = 1;
}

message GetGPIOInfoParamsResponse{
    int32 config_status = 1;            //【0标识配置失败，1标识配置成功】
    string gpio_module_info = 2;
}

message UpdateGPIOConfigItemParams{
    WorkSpaceInfo work_space_info = 1;
    string config_module = 2;           //GPIO、ADC、PWM、IIC、SPI、UART、EXIT
    // [name、PinInfo、des、config、spi_type、enable_config、spi_type、soft_spi_type、iic_type]其中一项，
    // spi_type、soft_spi_type、iic_type属于特殊模块的配置子项
    // config需要附带子项信息，config_item/work_mode
    string config_item = 3;
    string config_value = 4;            //例如config_value=输出
}

message UpdateGPIOConfigItemParamsResponse{
	  int32 config_status = 1;            //【0标识配置失败，1标识配置成功】
}
