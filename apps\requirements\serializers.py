from rest_framework import serializers


class RequirementListSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pagesize = serializers.IntegerField(default=10)
    type_list = serializers.ListField(child=serializers.CharField(), min_length=1)
    project_id = serializers.CharField()
    name = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    propertyList = serializers.ListField(child=serializers.CharField(), min_length=0, required=False, allow_null=True)
    reviewStatusList = serializers.ListField(child=serializers.CharField(), min_length=0, required=False,
                                             allow_null=True)
    multiReviewStatusList = serializers.ListField(child=serializers.CharField(), min_length=0, required=False,
                                                  allow_null=True)
    stateList = serializers.ListField(child=serializers.CharField(), min_length=0, required=False, allow_null=True)
    create_time = serializers.ListField(child=serializers.DateTimeField(), min_length=0, required=False,
                                        allow_null=True)
    update_time = serializers.ListField(child=serializers.DateTimeField(), min_length=0, required=False,
                                        allow_null=True)


class TestCaseSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    version = serializers.CharField()


class RequirementRelateTestCasesSerializer(serializers.Serializer):
    requirement_id = serializers.CharField()
    requirement_version = serializers.CharField()
    requirement_number = serializers.CharField()
    test_cases = serializers.ListField(child=TestCaseSerializer(), min_length=1)


class RequirementRelatedTestCasesSerializer(serializers.Serializer):
    requirement_id = serializers.CharField()
    requirement_version = serializers.CharField()


class RemoteRequirementsByTestCaseSerializer(serializers.Serializer):
    test_cases = serializers.ListField(child=serializers.CharField(), min_length=1)
    project_id = serializers.CharField()
    test_type = serializers.CharField()

